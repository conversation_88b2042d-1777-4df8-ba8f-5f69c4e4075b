// 神经网络与深度学习模块JavaScript代码

// 神经网络演示状态
let currentNeuralStep = 'neuron';
let animationRunning = false;
let animationInterval = null;

// 神经网络演示数据
const neuralSteps = {
    neuron: {
        title: "🧠 神经元基础",
        description: `
            <strong>什么是人工神经元？</strong><br>
            人工神经元是模拟生物神经元的数学模型。它接收多个输入信号，对这些信号进行加权求和，然后通过激活函数产生输出。<br><br>
            
            <strong>核心组成部分：</strong><br>
            • <strong>输入(Input)：</strong>来自其他神经元或外部数据的信号<br>
            • <strong>权重(Weight)：</strong>决定每个输入的重要程度<br>
            • <strong>偏置(Bias)：</strong>调节神经元的激活阈值<br>
            • <strong>激活函数：</strong>决定神经元是否被激活以及激活程度<br><br>
            
            <strong>数学表达：</strong><br>
            输出 = 激活函数(∑(输入 × 权重) + 偏置)<br><br>
            
            <strong>生活类比：</strong><br>
            想象神经元像一个"决策者"，它收集来自不同渠道的信息（输入），根据每个信息的可信度（权重）进行综合考虑，最后做出决定（输出）。
        `,
        visualization: "neuron"
    },
    perceptron: {
        title: "⚡ 感知器模型",
        description: `
            <strong>感知器是什么？</strong><br>
            感知器是最简单的神经网络模型，由单个神经元组成。它能够学习线性可分的模式，是神经网络发展的重要里程碑。<br><br>
            
            <strong>工作原理：</strong><br>
            1. 接收输入数据（如图像的像素值）<br>
            2. 计算加权和：∑(输入 × 权重) + 偏置<br>
            3. 通过阶跃函数判断：结果>0输出1，否则输出0<br>
            4. 根据错误调整权重（学习过程）<br><br>
            
            <strong>学习过程：</strong><br>
            • 如果预测正确，权重不变<br>
            • 如果预测错误，调整权重使下次预测更准确<br>
            • 重复这个过程直到模型收敛<br><br>
            
            <strong>局限性：</strong><br>
            感知器只能解决线性可分问题，无法处理XOR等非线性问题。这个局限推动了多层网络的发展。
        `,
        visualization: "perceptron"
    },
    multilayer: {
        title: "🏗️ 多层神经网络",
        description: `
            <strong>为什么需要多层？</strong><br>
            单层感知器无法解决非线性问题。通过添加隐藏层，神经网络获得了处理复杂模式的能力。<br><br>
            
            <strong>网络结构：</strong><br>
            • <strong>输入层：</strong>接收原始数据<br>
            • <strong>隐藏层：</strong>提取和组合特征（可以有多层）<br>
            • <strong>输出层：</strong>产生最终预测结果<br><br>
            
            <strong>信息流动：</strong><br>
            数据从输入层开始，逐层向前传播。每一层都对前一层的输出进行变换，逐步提取更高级的特征。<br><br>
            
            <strong>非线性能力：</strong><br>
            通过使用非线性激活函数（如ReLU、Sigmoid），多层网络能够学习复杂的非线性映射关系。<br><br>
            
            <strong>特征层次：</strong><br>
            • 第一层：学习简单特征（边缘、纹理）<br>
            • 中间层：组合简单特征形成复杂特征<br>
            • 最后层：基于高级特征做出决策
        `,
        visualization: "multilayer"
    },
    backprop: {
        title: "🔄 反向传播算法",
        description: `
            <strong>学习的核心机制</strong><br>
            反向传播是训练多层神经网络的关键算法。它通过计算梯度来调整网络中的权重，使网络能够从错误中学习。<br><br>
            
            <strong>算法步骤：</strong><br>
            1. <strong>前向传播：</strong>输入数据通过网络产生预测<br>
            2. <strong>计算损失：</strong>比较预测值与真实值的差异<br>
            3. <strong>反向传播：</strong>从输出层向输入层传播误差<br>
            4. <strong>更新权重：</strong>根据梯度调整每个连接的权重<br><br>
            
            <strong>梯度下降：</strong><br>
            想象在山坡上找最低点，梯度告诉我们最陡的下坡方向。我们沿着这个方向小步前进，最终到达山谷底部（最优解）。<br><br>
            
            <strong>链式法则：</strong><br>
            反向传播使用微积分的链式法则，将复杂网络的梯度计算分解为简单的局部计算。<br><br>
            
            <strong>为什么重要：</strong><br>
            没有反向传播，深度网络就无法有效训练。它是现代AI发展的基础算法。
        `,
        visualization: "backprop"
    },
    deep: {
        title: "🌊 深度学习",
        description: `
            <strong>深度的力量</strong><br>
            深度学习通过构建具有多个隐藏层的神经网络，能够自动学习数据的层次化表示，无需人工设计特征。<br><br>
            
            <strong>深度网络的优势：</strong><br>
            • <strong>自动特征学习：</strong>无需手工设计特征<br>
            • <strong>层次化表示：</strong>从简单到复杂的特征组合<br>
            • <strong>强大表达能力：</strong>能够逼近任意复杂函数<br>
            • <strong>端到端学习：</strong>直接从原始数据到最终结果<br><br>
            
            <strong>关键技术突破：</strong><br>
            • <strong>ReLU激活函数：</strong>解决梯度消失问题<br>
            • <strong>Dropout：</strong>防止过拟合<br>
            • <strong>批量归一化：</strong>加速训练过程<br>
            • <strong>残差连接：</strong>训练更深的网络<br><br>
            
            <strong>应用领域：</strong><br>
            • 计算机视觉（图像识别、目标检测）<br>
            • 自然语言处理（机器翻译、文本生成）<br>
            • 语音识别和合成<br>
            • 游戏AI（AlphaGo、游戏智能体）<br><br>
            
            <strong>深度学习 vs 传统机器学习：</strong><br>
            传统方法需要专家设计特征，深度学习能够自动发现最有用的特征表示。
        `,
        visualization: "deep"
    },
    llm: {
        title: "🤖 通向大语言模型",
        description: `
            <strong>从神经网络到LLM的演进</strong><br>
            大语言模型(LLM)是深度学习在自然语言处理领域的巅峰应用，它们基于Transformer架构，具有数十亿甚至数千亿参数。<br><br>
            
            <strong>关键发展历程：</strong><br>
            • <strong>RNN时代：</strong>循环神经网络处理序列数据<br>
            • <strong>LSTM/GRU：</strong>解决长序列记忆问题<br>
            • <strong>注意力机制：</strong>让模型关注重要信息<br>
            • <strong>Transformer：</strong>完全基于注意力的架构<br>
            • <strong>预训练模型：</strong>BERT、GPT等大规模预训练<br><br>
            
            <strong>LLM的神经网络基础：</strong><br>
            • <strong>规模：</strong>数十亿到数千亿参数的深度网络<br>
            • <strong>架构：</strong>基于Transformer的多层注意力网络<br>
            • <strong>训练：</strong>在海量文本数据上进行预训练<br>
            • <strong>涌现能力：</strong>规模达到临界点后出现的新能力<br><br>
            
            <strong>从基础到应用：</strong><br>
            1. 神经元 → 感知器 → 多层网络<br>
            2. 深度学习 → 注意力机制 → Transformer<br>
            3. 预训练 → 微调 → 大语言模型<br>
            4. ChatGPT、GPT-4等实际应用<br><br>
            
            <strong>未来展望：</strong><br>
            LLM正在向多模态、更大规模、更高效的方向发展，将继续推动AI技术的边界。
        `,
        visualization: "llm"
    }
};

function openNeuralDemo() {
    const modal = document.getElementById('neuralModal');
    const content = document.getElementById('neuralModalContent');

    content.innerHTML = createNeuralDemoContent();
    modal.style.display = 'block';

    // 防止背景滚动
    document.body.style.overflow = 'hidden';

    // 初始化神经网络演示
    initializeNeuralDemo();
}

function closeNeuralDemo() {
    // 停止任何正在运行的动画
    pauseAnimation();

    // 恢复背景滚动
    document.body.style.overflow = 'auto';

    document.getElementById('neuralModal').style.display = 'none';
}

function createNeuralDemoContent() {
    return `
        <div class="neural-demo-area">
            <div class="neural-header">
                <h2>🧠 神经网络与深度学习交互式演示</h2>
                <p>从单个神经元到深度网络的完整学习之旅</p>
            </div>
            
            <div class="neural-content">
                <div class="neural-sidebar">
                    <h3 style="color: #FFD700; margin-bottom: 20px;">📚 学习模块</h3>
                    
                    <div class="neural-step active" data-step="neuron">
                        <h4>1️⃣ 神经元基础</h4>
                        <p>理解人工神经元的工作原理，包括输入、权重、激活函数等核心概念</p>
                    </div>
                    
                    <div class="neural-step" data-step="perceptron">
                        <h4>2️⃣ 感知器模型</h4>
                        <p>学习最简单的神经网络模型，理解线性分类的基本原理</p>
                    </div>
                    
                    <div class="neural-step" data-step="multilayer">
                        <h4>3️⃣ 多层网络</h4>
                        <p>探索多层神经网络如何解决复杂的非线性问题</p>
                    </div>
                    
                    <div class="neural-step" data-step="backprop">
                        <h4>4️⃣ 反向传播</h4>
                        <p>理解神经网络如何通过反向传播算法进行学习</p>
                    </div>
                    
                    <div class="neural-step" data-step="deep">
                        <h4>5️⃣ 深度学习</h4>
                        <p>了解深度神经网络的特点和优势</p>
                    </div>
                    
                    <div class="neural-step" data-step="llm">
                        <h4>6️⃣ LLM连接</h4>
                        <p>理解神经网络如何发展为现代的大语言模型</p>
                    </div>
                </div>
                
                <div class="neural-main">
                    <div id="neuralVisualization" class="neural-visualization">
                        <!-- 可视化内容将通过JavaScript动态生成 -->
                    </div>
                    
                    <div class="control-panel">
                        <button class="control-btn active" onclick="startAnimation()">▶️ 开始演示</button>
                        <button class="control-btn" onclick="pauseAnimation()">⏸️ 暂停</button>
                        <button class="control-btn" onclick="resetAnimation()">🔄 重置</button>
                        <button class="control-btn" onclick="nextStep()">⏭️ 下一步</button>
                    </div>
                    
                    <div class="info-panel">
                        <h4 id="stepTitle">神经元基础</h4>
                        <p id="stepDescription">神经元是神经网络的基本单元...</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function initializeNeuralDemo() {
    // 初始化步骤点击事件
    document.querySelectorAll('.neural-step').forEach(step => {
        step.addEventListener('click', function () {
            const stepType = this.dataset.step;
            switchNeuralStep(stepType);
        });
    });

    // 显示初始步骤
    switchNeuralStep('neuron');
}

function switchNeuralStep(stepType) {
    // 停止当前动画
    pauseAnimation();

    currentNeuralStep = stepType;

    // 更新侧边栏激活状态
    document.querySelectorAll('.neural-step').forEach(step => {
        step.classList.remove('active');
    });
    const targetStep = document.querySelector(`[data-step="${stepType}"]`);
    if (targetStep) {
        targetStep.classList.add('active');
    }

    // 更新信息面板
    const stepData = neuralSteps[stepType];
    const titleElement = document.getElementById('stepTitle');
    const descElement = document.getElementById('stepDescription');

    if (titleElement && stepData) {
        titleElement.textContent = stepData.title;
    }
    if (descElement && stepData) {
        descElement.innerHTML = stepData.description;
    }

    // 更新可视化
    updateNeuralVisualization(stepType);

    // 重置按钮状态
    setTimeout(() => {
        updateControlButtons();
    }, 100);
}

function updateNeuralVisualization(stepType) {
    const container = document.getElementById('neuralVisualization');

    switch (stepType) {
        case 'neuron':
            container.innerHTML = createNeuronVisualization();
            break;
        case 'perceptron':
            container.innerHTML = createPerceptronVisualization();
            break;
        case 'multilayer':
            container.innerHTML = createMultilayerVisualization();
            break;
        case 'backprop':
            container.innerHTML = createBackpropVisualization();
            break;
        case 'deep':
            container.innerHTML = createDeepVisualization();
            break;
        case 'llm':
            container.innerHTML = createLLMVisualization();
            break;
    }
}

// 动画控制函数
function startAnimation() {
    if (animationRunning) return;

    // 先停止之前的动画
    pauseAnimation();

    animationRunning = true;

    // 更新按钮状态
    updateControlButtons();

    // 根据当前步骤启动相应演示
    switch (currentNeuralStep) {
        case 'neuron':
            demonstrateNeuronCalculation();
            break;
        case 'perceptron':
            demonstratePerceptronLearning();
            break;
        case 'multilayer':
            demonstrateFeatureHierarchy();
            break;
        case 'backprop':
            demonstrateBackpropagation();
            break;
        case 'deep':
            demonstrateDeepLearning();
            break;
        case 'llm':
            demonstrateLLMEvolution();
            break;
        default:
            // 对于没有特定动画的步骤，显示提示
            showAnimationMessage();
            break;
    }
}

function pauseAnimation() {
    animationRunning = false;
    if (animationInterval) {
        clearInterval(animationInterval);
        animationInterval = null;
    }
    updateControlButtons();
}

function resetAnimation() {
    pauseAnimation();
    updateNeuralVisualization(currentNeuralStep);
}

function updateControlButtons() {
    const startBtn = document.querySelector('button[onclick="startAnimation()"]');
    const pauseBtn = document.querySelector('button[onclick="pauseAnimation()"]');

    if (startBtn && pauseBtn) {
        if (animationRunning) {
            startBtn.classList.remove('active');
            pauseBtn.classList.add('active');
        } else {
            startBtn.classList.add('active');
            pauseBtn.classList.remove('active');
        }
    }
}

function showAnimationMessage() {
    // 为没有特定动画的步骤显示信息
    const neurons = document.querySelectorAll('.neuron');
    let index = 0;

    animationInterval = setInterval(() => {
        // 清除之前的激活状态
        neurons.forEach(n => n.classList.remove('active'));

        // 激活当前神经元
        if (neurons[index]) {
            neurons[index].classList.add('active');
        }

        index = (index + 1) % neurons.length;
    }, 800);
}

function nextStep() {
    const steps = ['neuron', 'perceptron', 'multilayer', 'backprop', 'deep', 'llm'];
    const currentIndex = steps.indexOf(currentNeuralStep);
    const nextIndex = (currentIndex + 1) % steps.length;
    switchNeuralStep(steps[nextIndex]);
}

// 可视化创建函数
function createNeuronVisualization() {
    return `
        <div class="neuron-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🧠 神经元结构与计算过程</h4>

                <div class="neuron-structure">
                    <div class="input-section">
                        <h5>输入信号</h5>
                        <div class="input-item">
                            <span class="input-label">x₁ = 0.8</span>
                            <div class="weight-line" data-weight="0.5">
                                <span class="weight-label">w₁ = 0.5</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₂ = 0.3</span>
                            <div class="weight-line" data-weight="0.3">
                                <span class="weight-label">w₂ = 0.3</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₃ = 0.6</span>
                            <div class="weight-line" data-weight="0.7">
                                <span class="weight-label">w₃ = 0.7</span>
                            </div>
                        </div>
                        <div class="bias-item">
                            <span class="bias-label">偏置 b = 0.1</span>
                        </div>
                    </div>

                    <div class="neuron-core">
                        <div class="neuron-body" id="neuronCore">
                            <div class="sum-symbol">Σ</div>
                            <div class="activation-func">σ</div>
                        </div>
                        <div class="computation-steps" id="computationSteps">
                            <div class="step">1. 加权求和</div>
                            <div class="step">2. 加偏置</div>
                            <div class="step">3. 激活函数</div>
                        </div>
                    </div>

                    <div class="output-section">
                        <h5>输出结果</h5>
                        <div class="output-value" id="outputValue">0.73</div>
                        <div class="output-explanation">
                            激活后的输出值
                        </div>
                    </div>
                </div>

                <div class="calculation-detail">
                    <h5 style="color: #FFD700;">📊 详细计算过程</h5>
                    <div class="calc-step">
                        <strong>步骤1：</strong> 加权求和 = x₁×w₁ + x₂×w₂ + x₃×w₃ = 0.8×0.5 + 0.3×0.3 + 0.6×0.7 = 0.89
                    </div>
                    <div class="calc-step">
                        <strong>步骤2：</strong> 加偏置 = 0.89 + 0.1 = 0.99
                    </div>
                    <div class="calc-step">
                        <strong>步骤3：</strong> 激活函数 σ(0.99) = 1/(1+e^(-0.99)) ≈ 0.73
                    </div>
                </div>

                <div class="activation-functions">
                    <h5 style="color: #FFD700;">⚡ 常见激活函数</h5>
                    <div class="func-grid">
                        <div class="func-item">
                            <strong>Sigmoid:</strong> σ(x) = 1/(1+e^(-x))<br>
                            <small>输出范围: (0,1)</small>
                        </div>
                        <div class="func-item">
                            <strong>ReLU:</strong> f(x) = max(0,x)<br>
                            <small>输出范围: [0,+∞)</small>
                        </div>
                        <div class="func-item">
                            <strong>Tanh:</strong> f(x) = (e^x-e^(-x))/(e^x+e^(-x))<br>
                            <small>输出范围: (-1,1)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}
