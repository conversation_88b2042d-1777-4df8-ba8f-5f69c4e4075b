// 演示数据和函数
const demoData = {
    embedding: {
        title: "🔍 输入嵌入演示",
        description: "观察文本如何转换为数字向量",
        interactive: true
    },
    positional: {
        title: "📍 位置编码演示",
        description: "理解位置信息如何添加到词向量中",
        interactive: true
    },
    attention: {
        title: "🎯 多头注意力演示",
        description: "可视化词汇间的注意力权重",
        interactive: true
    },
    feedforward: {
        title: "⚡ 前馈网络演示",
        description: "观察神经网络的前向传播过程",
        interactive: true
    },
    normalization: {
        title: "🔄 归一化与残差连接演示",
        description: "理解层归一化和残差连接的作用",
        interactive: true
    },
    output: {
        title: "📊 输出预测演示",
        description: "查看模型如何预测下一个词",
        interactive: true
    }
};

function openDemo(type) {
    const modal = document.getElementById('demoModal');
    const content = document.getElementById('modalContent');
    const demo = demoData[type];

    content.innerHTML = createDemoContent(type, demo);
    modal.style.display = 'block';

    // 添加特定演示的交互功能
    initializeDemoInteractions(type);
}

function closeDemo() {
    document.getElementById('demoModal').style.display = 'none';
    // 确保背景滚动恢复
    document.body.style.overflow = 'auto';
}

function createDemoContent(type, demo) {
    return `
                <h2>${demo.title}</h2>
                <p style="margin-bottom: 20px; color: #666;">${demo.description}</p>
                <div class="demo-area">
                    <input type="text" class="input-sentence" id="demoInput" 
                           placeholder="请输入一句话，例如：我喜欢学习人工智能" 
                           value="我喜欢学习人工智能">
                    <button class="process-btn" onclick="processDemo('${type}')">开始处理</button>
                    <button class="process-btn" onclick="clearDemo()" style="background: #dc3545;">清除结果</button>
                    <div class="result-area" id="demoResult">
                        <p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>
                    </div>
                </div>
            `;
}

function initializeDemoInteractions(type) {
    // 为每种演示类型初始化特定的交互功能
    const input = document.getElementById('demoInput');
    input.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            processDemo(type);
        }
    });
}

function processDemo(type) {
    const input = document.getElementById('demoInput').value;
    const result = document.getElementById('demoResult');

    if (!input.trim()) {
        result.innerHTML = '<p style="color: #dc3545;">请输入一句话！</p>';
        return;
    }

    // 根据不同类型生成不同的演示效果
    switch (type) {
        case 'embedding':
            showEmbeddingDemo(input, result);
            break;
        case 'positional':
            showPositionalDemo(input, result);
            break;
        case 'attention':
            showAttentionDemo(input, result);
            break;
        case 'feedforward':
            showFeedForwardDemo(input, result);
            break;
        case 'normalization':
            showNormalizationDemo(input, result);
            break;
        case 'output':
            showOutputDemo(input, result);
            break;
    }
}

function clearDemo() {
    document.getElementById('demoResult').innerHTML =
        '<p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>';
}

// 点击模态框外部关闭
window.onclick = function (event) {
    const modal = document.getElementById('demoModal');
    if (event.target === modal) {
        closeDemo();
    }
}

// 具体演示函数实现
function showEmbeddingDemo(input, result) {
    const tokens = input.split('');
    let html = '<h4>🔍 词汇嵌入演示 - 3D向量空间可视化</h4>';

    // 创建3D向量空间可视化
    html += '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin: 15px 0; color: white;">';
    html += '<h5 style="margin-bottom: 15px;">🌌 词汇在高维空间中的位置</h5>';
    html += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

    tokens.forEach((token, index) => {
        const x = (Math.random() * 200 - 100).toFixed(1);
        const y = (Math.random() * 200 - 100).toFixed(1);
        const z = (Math.random() * 200 - 100).toFixed(1);
        const similarity = Math.random();
        const color = `hsl(${similarity * 360}, 70%, 80%)`;

        html += `
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; border: 2px solid ${color};">
                    <div style="font-size: 1.5em; text-align: center; margin-bottom: 10px;">${token}</div>
                    <div style="font-family: monospace; font-size: 0.9em;">
                        <div>X: ${x}</div>
                        <div>Y: ${y}</div>
                        <div>Z: ${z}</div>
                    </div>
                    <div style="margin-top: 8px; font-size: 0.8em; opacity: 0.8;">
                        语义相似度: ${(similarity * 100).toFixed(1)}%
                    </div>
                </div>`;
    });

    html += '</div>';
    html += '<div style="margin-top: 15px; font-size: 0.9em; opacity: 0.9;">';
    html += '💡 相似颜色的词汇在语义空间中距离更近，表示含义相关';
    html += '</div>';
    html += '</div>';

    // 词汇相似度矩阵
    html += '<div style="margin: 20px 0;"><h5>📊 词汇相似度热力图</h5></div>';
    html += '<div style="display: grid; grid-template-columns: 50px repeat(' + tokens.length + ', 40px); gap: 2px; margin: 10px 0;">';

    // 表头
    html += '<div></div>';
    tokens.forEach(token => {
        html += `<div style="text-align: center; font-weight: bold; font-size: 0.8em;">${token}</div>`;
    });

    // 相似度矩阵
    for (let i = 0; i < tokens.length; i++) {
        html += `<div style="writing-mode: vertical-rl; text-align: center; font-weight: bold; font-size: 0.8em;">${tokens[i]}</div>`;
        for (let j = 0; j < tokens.length; j++) {
            const similarity = i === j ? 1 : Math.random() * 0.8 + 0.1;
            const intensity = Math.floor(similarity * 255);
            const color = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
            html += `<div style="background: ${color}; color: ${intensity > 128 ? 'white' : 'black'};
                        width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;
                        border-radius: 4px; font-size: 0.7em; font-weight: bold;">
                        ${similarity.toFixed(2)}
                    </div>`;
        }
    }

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">';
    html += '<strong>� 技术原理：</strong>每个字符被映射到512维向量空间，通过余弦相似度计算词汇间的语义距离。相似的词汇在空间中聚集，不同的词汇分散分布。';
    html += '</div>';

    result.innerHTML = html;
}

function showPositionalDemo(input, result) {
    const tokens = input.split('');
    let html = '<h4>📍 位置编码过程：</h4>';

    html += '<div style="margin: 15px 0;"><strong>1. 原始词向量 + 位置编码：</strong></div>';
    html += '<div style="display: grid; gap: 10px;">';

    tokens.forEach((token, index) => {
        const pos = index + 1;
        const posEncoding = Math.sin(pos / 10000).toFixed(3);
        html += `
                <div style="display: flex; align-items: center; padding: 10px; background: #f8f9fa; border-radius: 8px;">
                    <div style="flex: 1;">
                        <strong>位置 ${pos}:</strong> "${token}"
                    </div>
                    <div style="flex: 2; font-family: monospace;">
                        词向量 + 位置编码(${posEncoding})
                    </div>
                    <div style="width: 100px; height: 20px; background: linear-gradient(90deg,
                        hsl(${(pos * 30) % 360}, 70%, 80%),
                        hsl(${(pos * 30 + 60) % 360}, 70%, 80%));
                        border-radius: 10px;"></div>
                </div>`;
    });

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>位置编码使用正弦和余弦函数，让模型能够理解词汇的相对位置关系。';
    html += '</div>';

    result.innerHTML = html;
}

function showAttentionDemo(input, result) {
    const tokens = input.split('');
    const n = tokens.length;

    let html = '<h4>🎯 多头注意力机制：</h4>';
    html += '<div style="margin: 15px 0;"><strong>注意力权重矩阵（模拟）：</strong></div>';

    // 创建注意力矩阵
    html += '<div style="display: grid; grid-template-columns: 50px repeat(' + n + ', 40px); gap: 2px; margin: 10px 0;">';

    // 表头
    html += '<div></div>';
    tokens.forEach(token => {
        html += `<div style="text-align: center; font-weight: bold; font-size: 0.8em;">${token}</div>`;
    });

    // 注意力矩阵
    for (let i = 0; i < n; i++) {
        html += `<div style="writing-mode: vertical-rl; text-align: center; font-weight: bold; font-size: 0.8em;">${tokens[i]}</div>`;
        for (let j = 0; j < n; j++) {
            const attention = Math.random();
            const intensity = Math.floor(attention * 255);
            const color = `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
            html += `<div class="attention-cell" style="background: ${color}; color: ${intensity > 128 ? 'white' : 'black'};">
                        ${attention.toFixed(2)}
                    </div>`;
        }
    }

    html += '</div>';
    html += '<div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>颜色越深表示注意力权重越高，显示了每个字符对其他字符的关注程度。';
    html += '</div>';

    result.innerHTML = html;
}

function showFeedForwardDemo(input, result) {
    const tokens = input.split('');

    let html = '<h4>⚡ 前馈神经网络处理：</h4>';
    html += '<div style="margin: 15px 0;"><strong>网络层级处理过程：</strong></div>';

    tokens.forEach((token, index) => {
        html += `
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #4CAF50;">
                    <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的处理过程：</div>
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="padding: 8px 12px; background: #e3f2fd; border-radius: 5px; font-size: 0.9em;">
                            输入层<br><small>[512维]</small>
                        </div>
                        <div style="font-size: 1.5em;">→</div>
                        <div style="padding: 8px 12px; background: #fff3e0; border-radius: 5px; font-size: 0.9em;">
                            隐藏层<br><small>[2048维]</small>
                        </div>
                        <div style="font-size: 1.5em;">→</div>
                        <div style="padding: 8px 12px; background: #e8f5e8; border-radius: 5px; font-size: 0.9em;">
                            输出层<br><small>[512维]</small>
                        </div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #666;">
                        激活函数: ReLU | 参数量: ${(512 * 2048 + 2048 * 512).toLocaleString()}
                    </div>
                </div>`;
    });

    html += '<div style="margin-top: 15px; padding: 10px; background: #fff8e1; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>前馈网络对每个位置独立处理，通过非线性变换增强特征表达能力。';
    html += '</div>';

    result.innerHTML = html;
}

function showNormalizationDemo(input, result) {
    const tokens = input.split('');

    let html = '<h4>🔄 层归一化与残差连接：</h4>';

    tokens.forEach((token, index) => {
        const beforeNorm = Array.from({ length: 4 }, () => (Math.random() * 10 - 5).toFixed(2));
        const afterNorm = beforeNorm.map(x => ((x - 0) / 2.5).toFixed(2));

        html += `
                <div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                    <div style="font-weight: bold; margin-bottom: 10px;">字符 "${token}" 的归一化过程：</div>

                    <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 15px; align-items: center;">
                        <div>
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">归一化前:</div>
                            <div style="font-family: monospace; background: #ffebee; padding: 8px; border-radius: 4px;">
                                [${beforeNorm.join(', ')}]
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <div style="background: #2196F3; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8em;">
                                LayerNorm
                            </div>
                        </div>

                        <div>
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">归一化后:</div>
                            <div style="font-family: monospace; background: #e8f5e8; padding: 8px; border-radius: 4px;">
                                [${afterNorm.join(', ')}]
                            </div>
                        </div>
                    </div>

                    <div style="margin-top: 10px; padding: 8px; background: #e3f2fd; border-radius: 4px; font-size: 0.9em;">
                        <strong>残差连接:</strong> 输出 = LayerNorm(输入 + 子层输出)
                    </div>
                </div>`;
    });

    html += '<div style="margin-top: 15px; padding: 10px; background: #f3e5f5; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>层归一化稳定训练，残差连接帮助梯度传播，防止梯度消失问题。';
    html += '</div>';

    result.innerHTML = html;
}

function showOutputDemo(input, result) {
    const tokens = input.split('');
    const vocab = ['我', '你', '他', '喜', '欢', '学', '习', '人', '工', '智', '能', '的', '是', '在', '有'];

    let html = '<h4>📊 输出预测过程：</h4>';
    html += '<div style="margin: 15px 0;"><strong>预测下一个字符的概率分布：</strong></div>';

    // 生成概率分布
    const probabilities = vocab.map(() => Math.random()).sort((a, b) => b - a);
    const total = probabilities.reduce((sum, p) => sum + p, 0);
    const normalizedProbs = probabilities.map(p => p / total);

    html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0;">';
    html += '<div style="font-weight: bold; margin-bottom: 10px;">当前输入: "' + input + '"</div>';
    html += '<div style="font-weight: bold; margin-bottom: 10px;">预测下一个字符:</div>';

    vocab.forEach((char, index) => {
        const prob = normalizedProbs[index];
        const percentage = (prob * 100).toFixed(1);
        const barWidth = prob * 300;

        html += `
                <div style="display: flex; align-items: center; margin: 5px 0;">
                    <div style="width: 30px; text-align: center; font-weight: bold;">${char}</div>
                    <div style="flex: 1; margin: 0 10px;">
                        <div style="background: #e0e0e0; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: linear-gradient(90deg, #4CAF50, #8BC34A);
                                        height: 100%; width: ${barWidth}px;
                                        transition: width 0.5s ease;"></div>
                        </div>
                    </div>
                    <div style="width: 60px; text-align: right; font-family: monospace;">${percentage}%</div>
                </div>`;
    });

    html += '</div>';

    const topPrediction = vocab[0];
    html += `<div style="margin-top: 15px; padding: 15px; background: #e8f5e8; border-radius: 10px; border-left: 4px solid #4CAF50;">
                <strong>🎯 最终预测:</strong> "${topPrediction}" (概率: ${(normalizedProbs[0] * 100).toFixed(1)}%)
            </div>`;

    html += '<div style="margin-top: 15px; padding: 10px; background: #e1f5fe; border-radius: 5px;">';
    html += '<strong>💡 说明：</strong>通过softmax函数将最后的隐藏状态转换为词汇表上的概率分布，选择概率最高的词作为预测结果。';
    html += '</div>';

    result.innerHTML = html;
}

// 帮助功能
function toggleHelp() {
    const tooltip = document.getElementById('helpTooltip');
    tooltip.style.display = tooltip.style.display === 'block' ? 'none' : 'block';
}

// 可折叠功能
function toggleCollapsible(button) {
    button.classList.toggle('active');
    const content = button.nextElementSibling;
    const chevron = button.querySelector('.chevron');

    if (content.classList.contains('active')) {
        content.classList.remove('active');
        chevron.classList.remove('active');
    } else {
        content.classList.add('active');
        chevron.classList.add('active');
    }
}

// 更新进度条
function updateProgress() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / scrollHeight) * 100;
    document.getElementById('progressBar').style.width = progress + '%';
}

// 添加示例句子
const exampleSentences = [
    "我喜欢学习人工智能",
    "Transformer是强大的模型",
    "注意力机制很重要",
    "深度学习改变世界",
    "自然语言处理很有趣"
];

function getRandomExample() {
    return exampleSentences[Math.floor(Math.random() * exampleSentences.length)];
}

// 增强演示内容创建函数
function createDemoContent(type, demo) {
    const randomExample = getRandomExample();
    return `
                <h2>${demo.title}</h2>
                <p style="margin-bottom: 20px; color: #666;">${demo.description}</p>
                <div class="demo-area">
                    <div class="demo-controls">
                        <button class="control-btn" onclick="setExample('${randomExample}')">🎲 随机示例</button>
                        <button class="control-btn" onclick="setExample('我喜欢学习人工智能')">📝 默认示例</button>
                        <button class="control-btn" onclick="setExample('')">🗑️ 清空输入</button>
                    </div>
                    <input type="text" class="input-sentence" id="demoInput"
                           placeholder="请输入一句话，例如：我喜欢学习人工智能"
                           value="我喜欢学习人工智能">
                    <button class="process-btn" onclick="processDemo('${type}')">开始处理</button>
                    <button class="process-btn" onclick="clearDemo()" style="background: #dc3545;">清除结果</button>
                    <div class="result-area" id="demoResult">
                        <p style="color: #888; text-align: center;">点击"开始处理"查看演示效果</p>
                    </div>
                </div>
            `;
}

// 设置示例文本
function setExample(text) {
    document.getElementById('demoInput').value = text;
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
        closeDemo();
    }
});

// 滚动事件监听
window.addEventListener('scroll', updateProgress);

// 点击外部关闭帮助提示
document.addEventListener('click', function (e) {
    const helpButton = document.querySelector('.floating-help');
    const helpTooltip = document.getElementById('helpTooltip');
    if (!helpButton.contains(e.target)) {
        helpTooltip.style.display = 'none';
    }
});







function initializeNeuralDemo() {
    // 初始化步骤点击事件
    document.querySelectorAll('.neural-step').forEach(step => {
        step.addEventListener('click', function () {
            const stepType = this.dataset.step;
            switchNeuralStep(stepType);
        });
    });

    // 显示初始步骤
    switchNeuralStep('neuron');
}

function switchNeuralStep(stepType) {
    // 停止当前动画
    pauseAnimation();

    currentNeuralStep = stepType;

    // 更新侧边栏激活状态
    document.querySelectorAll('.neural-step').forEach(step => {
        step.classList.remove('active');
    });
    const targetStep = document.querySelector(`[data-step="${stepType}"]`);
    if (targetStep) {
        targetStep.classList.add('active');
    }

    // 更新信息面板
    const stepData = neuralSteps[stepType];
    const titleElement = document.getElementById('stepTitle');
    const descElement = document.getElementById('stepDescription');

    if (titleElement && stepData) {
        titleElement.textContent = stepData.title;
    }
    if (descElement && stepData) {
        descElement.innerHTML = stepData.description;
    }

    // 更新可视化
    updateNeuralVisualization(stepType);

    // 重置按钮状态
    setTimeout(() => {
        updateControlButtons();
    }, 100);
}

function updateNeuralVisualization(stepType) {
    const container = document.getElementById('neuralVisualization');

    switch (stepType) {
        case 'neuron':
            container.innerHTML = createNeuronVisualization();
            break;
        case 'perceptron':
            container.innerHTML = createPerceptronVisualization();
            break;
        case 'multilayer':
            container.innerHTML = createMultilayerVisualization();
            break;
        case 'backprop':
            container.innerHTML = createBackpropVisualization();
            break;
        case 'deep':
            container.innerHTML = createDeepVisualization();
            break;
        case 'llm':
            container.innerHTML = createLLMVisualization();
            break;
    }
}

function createNeuronVisualization() {
    return `
        <div class="neuron-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🧠 神经元结构与计算过程</h4>

                <div class="neuron-structure">
                    <div class="input-section">
                        <h5>输入信号</h5>
                        <div class="input-item">
                            <span class="input-label">x₁ = 0.8</span>
                            <div class="weight-line" data-weight="0.5">
                                <span class="weight-label">w₁ = 0.5</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₂ = 0.3</span>
                            <div class="weight-line" data-weight="0.3">
                                <span class="weight-label">w₂ = 0.3</span>
                            </div>
                        </div>
                        <div class="input-item">
                            <span class="input-label">x₃ = 0.6</span>
                            <div class="weight-line" data-weight="0.7">
                                <span class="weight-label">w₃ = 0.7</span>
                            </div>
                        </div>
                        <div class="bias-item">
                            <span class="bias-label">偏置 b = 0.1</span>
                        </div>
                    </div>

                    <div class="neuron-core">
                        <div class="neuron-body" id="neuronCore">
                            <div class="sum-symbol">Σ</div>
                            <div class="activation-func">σ</div>
                        </div>
                        <div class="computation-steps" id="computationSteps">
                            <div class="step">1. 加权求和</div>
                            <div class="step">2. 加偏置</div>
                            <div class="step">3. 激活函数</div>
                        </div>
                    </div>

                    <div class="output-section">
                        <h5>输出结果</h5>
                        <div class="output-value" id="outputValue">0.73</div>
                        <div class="output-explanation">
                            激活后的输出值
                        </div>
                    </div>
                </div>

                <div class="calculation-detail">
                    <h5 style="color: #FFD700;">📊 详细计算过程</h5>
                    <div class="calc-step">
                        <strong>步骤1：</strong> 加权求和 = x₁×w₁ + x₂×w₂ + x₃×w₃ = 0.8×0.5 + 0.3×0.3 + 0.6×0.7 = 0.89
                    </div>
                    <div class="calc-step">
                        <strong>步骤2：</strong> 加偏置 = 0.89 + 0.1 = 0.99
                    </div>
                    <div class="calc-step">
                        <strong>步骤3：</strong> 激活函数 σ(0.99) = 1/(1+e^(-0.99)) ≈ 0.73
                    </div>
                </div>

                <div class="activation-functions">
                    <h5 style="color: #FFD700;">⚡ 常见激活函数</h5>
                    <div class="func-grid">
                        <div class="func-item">
                            <strong>Sigmoid:</strong> σ(x) = 1/(1+e^(-x))<br>
                            <small>输出范围: (0,1)</small>
                        </div>
                        <div class="func-item">
                            <strong>ReLU:</strong> f(x) = max(0,x)<br>
                            <small>输出范围: [0,+∞)</small>
                        </div>
                        <div class="func-item">
                            <strong>Tanh:</strong> f(x) = (e^x-e^(-x))/(e^x+e^(-x))<br>
                            <small>输出范围: (-1,1)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createPerceptronVisualization() {
    return `
        <div class="perceptron-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">⚡ 感知器：二分类学习器</h4>

                <div class="perceptron-example">
                    <h5>实例：判断是否为运动员</h5>
                    <div class="training-data">
                        <div class="data-table">
                            <div class="table-header">
                                <span>身高(cm)</span>
                                <span>体重(kg)</span>
                                <span>年龄</span>
                                <span>标签</span>
                            </div>
                            <div class="table-row positive">
                                <span>185</span><span>80</span><span>25</span><span class="label athlete">运动员</span>
                            </div>
                            <div class="table-row positive">
                                <span>178</span><span>75</span><span>28</span><span class="label athlete">运动员</span>
                            </div>
                            <div class="table-row negative">
                                <span>165</span><span>60</span><span>35</span><span class="label normal">普通人</span>
                            </div>
                            <div class="table-row negative">
                                <span>170</span><span>65</span><span>40</span><span class="label normal">普通人</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="perceptron-structure">
                    <h5 style="color: #FFD700;">🔄 感知器学习过程</h5>
                    <div class="learning-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <strong>初始化权重</strong><br>
                                随机设置权重：w₁=0.2, w₂=0.3, w₃=0.1, b=0.5
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <strong>前向计算</strong><br>
                                输入样本 → 计算加权和 → 激活函数 → 预测结果
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <strong>误差计算</strong><br>
                                比较预测值与真实标签，计算误差
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <strong>权重更新</strong><br>
                                根据误差调整权重：w = w + η × (真实值 - 预测值) × 输入
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <strong>重复训练</strong><br>
                                重复步骤2-4，直到模型收敛或达到最大迭代次数
                            </div>
                        </div>
                    </div>
                </div>

                <div class="decision-boundary">
                    <h5 style="color: #FFD700;">📊 决策边界可视化</h5>
                    <div class="boundary-explanation">
                        <p>感知器学习的本质是找到一个超平面（在2D中是直线），将不同类别的数据分开。</p>
                        <div class="boundary-formula">
                            <strong>决策边界方程：</strong> w₁×身高 + w₂×体重 + w₃×年龄 + b = 0
                        </div>
                        <div class="classification-rule">
                            <div class="rule-item positive-rule">
                                <strong>运动员：</strong> 当 w₁×x₁ + w₂×x₂ + w₃×x₃ + b > 0
                            </div>
                            <div class="rule-item negative-rule">
                                <strong>普通人：</strong> 当 w₁×x₁ + w₂×x₂ + w₃×x₃ + b ≤ 0
                            </div>
                        </div>
                    </div>
                </div>

                <div class="limitations">
                    <h5 style="color: #FFD700;">⚠️ 感知器的局限性</h5>
                    <div class="limitation-grid">
                        <div class="limitation-item">
                            <strong>线性可分性：</strong><br>
                            只能解决线性可分的问题，无法处理XOR等非线性问题
                        </div>
                        <div class="limitation-item">
                            <strong>单层结构：</strong><br>
                            只有一层权重，表达能力有限
                        </div>
                        <div class="limitation-item">
                            <strong>二分类限制：</strong><br>
                            原始感知器只能进行二分类任务
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createMultilayerVisualization() {
    return `
        <div class="multilayer-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🏗️ 多层神经网络：突破线性限制</h4>

                <div class="network-evolution">
                    <h5>从单层到多层的演进</h5>
                    <div class="evolution-comparison">
                        <div class="evolution-item">
                            <h6>单层感知器</h6>
                            <div class="simple-network">
                                <div class="layer-simple">输入</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">输出</div>
                            </div>
                            <div class="capability">只能解决线性可分问题</div>
                        </div>
                        <div class="evolution-item">
                            <h6>多层神经网络</h6>
                            <div class="complex-network">
                                <div class="layer-simple">输入</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">隐藏层</div>
                                <div class="arrow">→</div>
                                <div class="layer-simple">输出</div>
                            </div>
                            <div class="capability">可以解决非线性问题</div>
                        </div>
                    </div>
                </div>

                <div class="xor-problem">
                    <h5 style="color: #FFD700;">🔍 经典案例：XOR问题</h5>
                    <div class="xor-explanation">
                        <div class="xor-table">
                            <h6>XOR真值表</h6>
                            <table>
                                <tr><th>输入A</th><th>输入B</th><th>输出</th></tr>
                                <tr><td>0</td><td>0</td><td>0</td></tr>
                                <tr><td>0</td><td>1</td><td>1</td></tr>
                                <tr><td>1</td><td>0</td><td>1</td></tr>
                                <tr><td>1</td><td>1</td><td>0</td></tr>
                            </table>
                        </div>
                        <div class="xor-solution">
                            <h6>多层网络解决方案</h6>
                            <div class="xor-network">
                                <div class="xor-layer">
                                    <div class="xor-title">输入层</div>
                                    <div class="xor-node">A</div>
                                    <div class="xor-node">B</div>
                                </div>
                                <div class="xor-layer">
                                    <div class="xor-title">隐藏层</div>
                                    <div class="xor-node">AND门</div>
                                    <div class="xor-node">OR门</div>
                                </div>
                                <div class="xor-layer">
                                    <div class="xor-title">输出层</div>
                                    <div class="xor-node">XOR</div>
                                </div>
                            </div>
                            <div class="xor-formula">
                                XOR = OR AND (NOT AND)
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-hierarchy">
                    <h5 style="color: #FFD700;">📊 特征层次化学习</h5>
                    <div class="hierarchy-example">
                        <div class="hierarchy-title">图像识别中的特征提取</div>
                        <div class="hierarchy-layers">
                            <div class="hierarchy-layer">
                                <div class="layer-name">输入层</div>
                                <div class="layer-features">原始像素</div>
                                <div class="feature-examples">
                                    <div class="pixel-grid">
                                        <div class="pixel"></div><div class="pixel"></div><div class="pixel"></div>
                                        <div class="pixel"></div><div class="pixel active"></div><div class="pixel"></div>
                                        <div class="pixel"></div><div class="pixel"></div><div class="pixel"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">隐藏层1</div>
                                <div class="layer-features">边缘检测</div>
                                <div class="feature-examples">
                                    <div class="edge-patterns">
                                        <div class="edge horizontal"></div>
                                        <div class="edge vertical"></div>
                                        <div class="edge diagonal"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">隐藏层2</div>
                                <div class="layer-features">形状组合</div>
                                <div class="feature-examples">
                                    <div class="shape-patterns">
                                        <div class="shape circle"></div>
                                        <div class="shape triangle"></div>
                                        <div class="shape square"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="hierarchy-arrow">→</div>
                            <div class="hierarchy-layer">
                                <div class="layer-name">输出层</div>
                                <div class="layer-features">对象识别</div>
                                <div class="feature-examples">
                                    <div class="object-labels">
                                        <div class="label">猫</div>
                                        <div class="label">狗</div>
                                        <div class="label">鸟</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="universal-approximation">
                    <h5 style="color: #FFD700;">🎯 万能逼近定理</h5>
                    <div class="theorem-explanation">
                        <div class="theorem-statement">
                            <strong>定理：</strong>具有足够多隐藏单元的单隐藏层前馈网络可以逼近任何连续函数到任意精度。
                        </div>
                        <div class="theorem-implications">
                            <div class="implication">
                                <strong>理论意义：</strong>多层网络具有强大的表达能力
                            </div>
                            <div class="implication">
                                <strong>实践意义：</strong>深度网络通常比宽度网络更高效
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 动画控制函数
function startAnimation() {
    if (animationRunning) return;

    // 先停止之前的动画
    pauseAnimation();

    animationRunning = true;

    // 更新按钮状态
    updateControlButtons();

    // 根据当前步骤启动相应演示
    switch (currentNeuralStep) {
        case 'neuron':
            demonstrateNeuronCalculation();
            break;
        case 'perceptron':
            demonstratePerceptronLearning();
            break;
        case 'multilayer':
            demonstrateFeatureHierarchy();
            break;
        case 'backprop':
            demonstrateBackpropagation();
            break;
        case 'deep':
            demonstrateDeepLearning();
            break;
        case 'llm':
            demonstrateLLMEvolution();
            break;
        default:
            // 对于没有特定动画的步骤，显示提示
            showAnimationMessage();
            break;
    }
}

function pauseAnimation() {
    animationRunning = false;
    if (animationInterval) {
        clearInterval(animationInterval);
        animationInterval = null;
    }
    updateControlButtons();
}

function resetAnimation() {
    pauseAnimation();
    updateNeuralVisualization(currentNeuralStep);
}

function updateControlButtons() {
    const startBtn = document.querySelector('button[onclick="startAnimation()"]');
    const pauseBtn = document.querySelector('button[onclick="pauseAnimation()"]');

    if (startBtn && pauseBtn) {
        if (animationRunning) {
            startBtn.classList.remove('active');
            pauseBtn.classList.add('active');
        } else {
            startBtn.classList.add('active');
            pauseBtn.classList.remove('active');
        }
    }
}

function showAnimationMessage() {
    // 为没有特定动画的步骤显示信息
    const neurons = document.querySelectorAll('.neuron');
    let index = 0;

    animationInterval = setInterval(() => {
        // 清除之前的激活状态
        neurons.forEach(n => n.classList.remove('active'));

        // 激活当前神经元
        if (neurons[index]) {
            neurons[index].classList.add('active');
        }

        index = (index + 1) % neurons.length;
    }, 800);
}

function nextStep() {
    const steps = ['neuron', 'perceptron', 'multilayer', 'backprop', 'deep', 'llm'];
    const currentIndex = steps.indexOf(currentNeuralStep);
    const nextIndex = (currentIndex + 1) % steps.length;
    switchNeuralStep(steps[nextIndex]);
}

// 创建剩余的可视化函数
function createBackpropVisualization() {
    return `
        <div class="backprop-demo-container">
            <div class="demo-section">
                <h4 style="color: #FFD700; margin-bottom: 15px;">🔄 反向传播：神经网络的学习算法</h4>

                <div class="backprop-overview">
                    <h5>算法核心思想</h5>
                    <div class="algorithm-steps">
                        <div class="algo-step forward">
                            <div class="step-icon">→</div>
                            <div class="step-content">
                                <strong>前向传播</strong><br>
                                输入数据通过网络，计算预测输出
                            </div>
                        </div>
                        <div class="algo-step loss">
                            <div class="step-icon">📊</div>
                            <div class="step-content">
                                <strong>损失计算</strong><br>
                                比较预测值与真实值，计算误差
                            </div>
                        </div>
                        <div class="algo-step backward">
                            <div class="step-icon">←</div>
                            <div class="step-content">
                                <strong>反向传播</strong><br>
                                误差从输出层向输入层传播
                            </div>
                        </div>
                        <div class="algo-step update">
                            <div class="step-icon">🔧</div>
                            <div class="step-content">
                                <strong>权重更新</strong><br>
                                根据梯度调整网络参数
                            </div>
                        </div>
                    </div>
                </div>

                <div class="gradient-descent">
                    <h5 style="color: #FFD700;">📈 梯度下降可视化</h5>
                    <div class="gradient-explanation">
                        <div class="mountain-analogy">
                            <h6>🏔️ 山坡类比</h6>
                            <div class="mountain-visual">
                                <div class="mountain-curve">
                                    <div class="ball" id="gradientBall">⚫</div>
                                    <div class="valley">最优解</div>
                                </div>
                            </div>
                            <p>想象在山坡上找最低点：</p>
                            <ul>
                                <li><strong>当前位置：</strong>网络的当前参数状态</li>
                                <li><strong>坡度：</strong>损失函数的梯度</li>
                                <li><strong>下山方向：</strong>梯度的反方向</li>
                                <li><strong>步长：</strong>学习率（每次移动的距离）</li>
                            </ul>
                        </div>

                        <div class="gradient-formula">
                            <h6>📐 数学公式</h6>
                            <div class="formula-box">
                                <div class="formula-item">
                                    <strong>权重更新规则：</strong><br>
                                    w_new = w_old - η × ∂L/∂w
                                </div>
                                <div class="formula-item">
                                    <strong>链式法则：</strong><br>
                                    ∂L/∂w = ∂L/∂y × ∂y/∂z × ∂z/∂w
                                </div>
                            </div>
                            <div class="formula-explanation">
                                <p><strong>η (eta)：</strong>学习率，控制更新步长</p>
                                <p><strong>∂L/∂w：</strong>损失函数对权重的偏导数（梯度）</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="backprop-example">
                    <h5 style="color: #FFD700;">🔢 具体计算示例</h5>
                    <div class="calculation-flow">
                        <div class="calc-phase">
                            <h6>前向传播</h6>
                            <div class="calc-steps">
                                <div class="calc-item">输入: x = 2</div>
                                <div class="calc-item">权重: w = 0.5</div>
                                <div class="calc-item">预测: ŷ = x × w = 1.0</div>
                                <div class="calc-item">真实值: y = 3</div>
                                <div class="calc-item">损失: L = ½(y - ŷ)² = 2.0</div>
                            </div>
                        </div>

                        <div class="calc-phase">
                            <h6>反向传播</h6>
                            <div class="calc-steps">
                                <div class="calc-item">∂L/∂ŷ = -(y - ŷ) = -2</div>
                                <div class="calc-item">∂ŷ/∂w = x = 2</div>
                                <div class="calc-item">∂L/∂w = ∂L/∂ŷ × ∂ŷ/∂w = -4</div>
                                <div class="calc-item">w_new = w - η × ∂L/∂w = 0.5 - 0.1 × (-4) = 0.9</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="backprop-advantages">
                    <h5 style="color: #FFD700;">✨ 反向传播的重要性</h5>
                    <div class="advantages-grid">
                        <div class="advantage-item">
                            <strong>高效计算：</strong><br>
                            一次反向传播可以计算所有参数的梯度
                        </div>
                        <div class="advantage-item">
                            <strong>自动微分：</strong><br>
                            无需手动计算复杂的导数
                        </div>
                        <div class="advantage-item">
                            <strong>可扩展性：</strong><br>
                            适用于任意深度和复杂度的网络
                        </div>
                        <div class="advantage-item">
                            <strong>现代AI基础：</strong><br>
                            几乎所有深度学习框架的核心算法
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function createDeepVisualization() {
    return `
        <div class="network-container" style="justify-content: space-between;">
            <div class="layer">
                <div class="layer-title">输入</div>
                <div class="neuron">像素1</div>
                <div class="neuron">像素2</div>
                <div class="neuron">像素3</div>
                <div class="neuron">...</div>
                <div class="neuron">像素n</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层1</div>
                <div class="neuron">边缘</div>
                <div class="neuron">纹理</div>
                <div class="neuron">形状</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层2</div>
                <div class="neuron">眼睛</div>
                <div class="neuron">鼻子</div>
                <div class="neuron">嘴巴</div>
            </div>

            <div class="layer">
                <div class="layer-title">特征层3</div>
                <div class="neuron">面部</div>
                <div class="neuron">表情</div>
            </div>

            <div class="layer">
                <div class="layer-title">分类</div>
                <div class="neuron">人脸</div>
                <div class="neuron">非人脸</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px; color: white;">
            <p>深度学习：从像素到高级概念的层次化特征学习</p>
            <p style="font-size: 0.9em; opacity: 0.8;">每一层都在前一层的基础上学习更抽象的特征</p>
        </div>
    `;
}

function createLLMVisualization() {
    return `
        <div class="network-container" style="flex-direction: column; gap: 30px;">
            <div style="display: flex; justify-content: space-around; width: 100%;">
                <div class="layer">
                    <div class="layer-title">传统神经网络</div>
                    <div style="display: flex; gap: 10px;">
                        <div class="neuron small">输入</div>
                        <div class="neuron small">隐藏</div>
                        <div class="neuron small">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数百个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">深度网络</div>
                    <div style="display: flex; gap: 5px;">
                        <div class="neuron small">输入</div>
                        <div class="neuron small">层1</div>
                        <div class="neuron small">层2</div>
                        <div class="neuron small">层3</div>
                        <div class="neuron small">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数万个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">Transformer</div>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 3px;">
                        <div class="neuron tiny">注意力</div>
                        <div class="neuron tiny">前馈</div>
                        <div class="neuron tiny">归一化</div>
                        <div class="neuron tiny">残差</div>
                        <div class="neuron tiny">多头</div>
                        <div class="neuron tiny">编码</div>
                        <div class="neuron tiny">解码</div>
                        <div class="neuron tiny">输出</div>
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数亿个参数</p>
                </div>

                <div class="layer">
                    <div class="layer-title">大语言模型</div>
                    <div style="display: grid; grid-template-columns: repeat(6, 1fr); gap: 2px;">
                        ${Array(24).fill().map(() => '<div class="neuron tiny">T</div>').join('')}
                    </div>
                    <p style="color: #FFD700; font-size: 0.8em; margin-top: 10px;">数千亿参数</p>
                </div>
            </div>

            <div style="text-align: center; color: white;">
                <h4 style="color: #FFD700; margin-bottom: 15px;">从神经网络到LLM的演进</h4>
                <p>参数规模的指数级增长 → 涌现能力的出现 → 通用人工智能的曙光</p>
            </div>
        </div>
    `;
}

// 动画函数
// 新的交互式演示函数 - 替代简单的动画
function demonstrateNeuronCalculation() {
    // 为神经元演示添加交互式计算
    const steps = document.querySelectorAll('.calc-step');
    let currentStep = 0;

    const highlightStep = () => {
        steps.forEach(step => step.style.background = 'rgba(255, 255, 255, 0.05)');
        if (steps[currentStep]) {
            steps[currentStep].style.background = 'rgba(255, 215, 0, 0.3)';
            steps[currentStep].style.border = '2px solid #FFD700';
        }
        currentStep = (currentStep + 1) % steps.length;
    };

    // 每2秒高亮下一步
    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightStep();
    }, 2000);
}

function demonstratePerceptronLearning() {
    // 高亮学习步骤
    const stepItems = document.querySelectorAll('.step-item');
    let currentStep = 0;

    const highlightLearningStep = () => {
        stepItems.forEach(item => {
            item.style.background = 'rgba(255, 255, 255, 0.05)';
            item.style.border = 'none';
        });

        if (stepItems[currentStep]) {
            stepItems[currentStep].style.background = 'rgba(76, 175, 80, 0.3)';
            stepItems[currentStep].style.border = '2px solid #4CAF50';
        }
        currentStep = (currentStep + 1) % stepItems.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightLearningStep();
    }, 1800);
}

function demonstrateFeatureHierarchy() {
    // 演示特征层次化学习
    const hierarchyLayers = document.querySelectorAll('.hierarchy-layer');
    let currentLayer = 0;

    const highlightLayer = () => {
        hierarchyLayers.forEach(layer => {
            layer.style.background = 'transparent';
            layer.style.border = 'none';
        });

        if (hierarchyLayers[currentLayer]) {
            hierarchyLayers[currentLayer].style.background = 'rgba(78, 205, 196, 0.2)';
            hierarchyLayers[currentLayer].style.border = '2px solid #4ECDC4';
            hierarchyLayers[currentLayer].style.borderRadius = '10px';
            hierarchyLayers[currentLayer].style.padding = '15px';
        }
        currentLayer = (currentLayer + 1) % hierarchyLayers.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightLayer();
    }, 2500);
}

// 添加剩余的演示函数
function demonstrateBackpropagation() {
    // 演示反向传播的计算流程
    const calcPhases = document.querySelectorAll('.calc-phase');
    let currentPhase = 0;

    const highlightPhase = () => {
        calcPhases.forEach(phase => {
            phase.style.background = 'rgba(255, 255, 255, 0.05)';
            phase.style.border = 'none';
        });

        if (calcPhases[currentPhase]) {
            calcPhases[currentPhase].style.background = 'rgba(255, 107, 107, 0.3)';
            calcPhases[currentPhase].style.border = '2px solid #FF6B6B';
            calcPhases[currentPhase].style.borderRadius = '10px';
            calcPhases[currentPhase].style.padding = '15px';
        }
        currentPhase = (currentPhase + 1) % calcPhases.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightPhase();
    }, 3000);
}

function demonstrateDeepLearning() {
    // 演示深度学习的优势
    const advantageItems = document.querySelectorAll('.advantage-item, .breakthrough-item');
    let currentItem = 0;

    const highlightAdvantage = () => {
        advantageItems.forEach(item => {
            item.style.background = 'rgba(255, 255, 255, 0.05)';
            item.style.transform = 'scale(1)';
        });

        if (advantageItems[currentItem]) {
            advantageItems[currentItem].style.background = 'rgba(78, 205, 196, 0.3)';
            advantageItems[currentItem].style.transform = 'scale(1.05)';
        }
        currentItem = (currentItem + 1) % advantageItems.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightAdvantage();
    }, 2000);
}

function demonstrateLLMEvolution() {
    // 演示从神经网络到LLM的演进
    const evolutionStages = document.querySelectorAll('.evolution-stage, .llm-capability');
    let currentStage = 0;

    const highlightEvolution = () => {
        evolutionStages.forEach(stage => {
            stage.style.background = 'rgba(255, 255, 255, 0.05)';
            stage.style.border = 'none';
        });

        if (evolutionStages[currentStage]) {
            evolutionStages[currentStage].style.background = 'rgba(255, 215, 0, 0.3)';
            evolutionStages[currentStage].style.border = '2px solid #FFD700';
            evolutionStages[currentStage].style.borderRadius = '10px';
        }
        currentStage = (currentStage + 1) % evolutionStages.length;
    };

    animationInterval = setInterval(() => {
        if (!animationRunning) {
            clearInterval(animationInterval);
            return;
        }
        highlightEvolution();
    }, 2800);
}

// 键盘事件支持
document.addEventListener('keydown', function (e) {
    const neuralModal = document.getElementById('neuralModal');
    const demoModal = document.getElementById('demoModal');

    if (e.key === 'Escape') {
        if (neuralModal && neuralModal.style.display === 'block') {
            closeNeuralDemo();
        }
        if (demoModal && demoModal.style.display === 'block') {
            closeDemo();
        }
    }

    // 在神经网络演示中的快捷键
    if (neuralModal.style.display === 'block') {
        switch (e.key) {
            case 'ArrowRight':
            case ' ':
                e.preventDefault();
                nextStep();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                // 上一步
                const steps = ['neuron', 'perceptron', 'multilayer', 'backprop', 'deep', 'llm'];
                const currentIndex = steps.indexOf(currentNeuralStep);
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : steps.length - 1;
                switchNeuralStep(steps[prevIndex]);
                break;
            case 'Enter':
                e.preventDefault();
                if (!animationRunning) {
                    startAnimation();
                } else {
                    pauseAnimation();
                }
                break;
            case 'r':
            case 'R':
                e.preventDefault();
                resetAnimation();
                break;
        }
    }
});

// 旧的动画函数已被新的交互式演示替代

// 添加页面加载动画
document.addEventListener('DOMContentLoaded', function () {
    const modules = document.querySelectorAll('.module');
    modules.forEach((module, index) => {
        setTimeout(() => {
            module.style.opacity = '1';
            module.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // 初始化进度条
    updateProgress();
});